import { DictionarieState } from '@/models/dictionarie';
import { create, index, remove, update } from '@/services/additional-service';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { connect } from '@umijs/max';
import { Button, message, Popconfirm, Space } from 'antd';
import React, { useRef, useState } from 'react';
import AdditionalServiceDurationModal from './AdditionalServiceDurationModal';
import EditModal from './EditModal';

const AdditionalService: React.FC<{ dictionarie: DictionarieState }> = ({
  dictionarie,
}) => {
  const actionRef = useRef<ActionType>();
  const [modalVisible, setModalVisible] = useState(false);
  const [current, setCurrent] = useState<
    API.AdditionalServiceAttrs | undefined
  >(undefined);
  const [durationModalVisible, setDurationModalVisible] =
    useState<boolean>(false);

  const serviceTypeList =
    dictionarie?.list?.filter((item) => item.type === '增项服务类型') || [];
  const typeValueEnum: { [key: string]: string } = {};
  serviceTypeList.forEach((item) => {
    typeValueEnum[item.code] = item.name;
  });

  const handleSave = async (values: API.AdditionalServiceAttrs) => {
    let response;
    if (current) {
      const { id, ...info } = values;
      response = await update(id, info);
    } else {
      response = await create(values);
    }

    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('操作成功');
      actionRef?.current?.reload();
      setModalVisible(false);
    }
  };

  const handleDel = async (record: API.AdditionalServiceAttrs) => {
    const { id } = record;
    const response = await remove(id);
    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('删除成功');
      actionRef?.current?.reload();
    }
  };

  const columns: ProColumns<API.AdditionalServiceAttrs, 'text'>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      hideInSearch: true,
      hidden: true,
    },
    {
      title: 'logo',
      dataIndex: 'logo',
      hideInSearch: true,
      valueType: 'image',
    },
    {
      title: '服务名称',
      dataIndex: 'name',
    },
    {
      title: '服务类型',
      dataIndex: 'type',
      valueEnum: typeValueEnum,
      filters: true,
    },
    {
      title: '服务价格',
      dataIndex: 'price',
      hideInSearch: true,
      valueType: 'money',
    },
    {
      title: '平均时长',
      tooltip: '取最近10次记录的平均时长',
      dataIndex: 'duration',
      hideInSearch: true,
      render: (_, record) => (record.duration ? `${record.duration}分钟` : '-'),
    },
    {
      title: '统计时长',
      dataIndex: 'needDurationTracking',
      hideInSearch: true,
      valueType: 'switch',
      render: (_, record) => (
        <span style={{ color: record.needDurationTracking ? '#52c41a' : '#d9d9d9' }}>
          {record.needDurationTracking ? '是' : '否'}
        </span>
      ),
    },
    {
      title: '服务说明',
      dataIndex: 'description',
      hideInSearch: true,
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'action',
      valueType: 'option',
      align: 'center',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            onClick={() => {
              setCurrent(record);
              setModalVisible(true);
            }}
          >
            编辑
          </Button>
          <Button
            type="link"
            onClick={() => {
              setCurrent(record);
              setDurationModalVisible(true);
            }}
            disabled={!record.needDurationTracking}
            title={
              record.needDurationTracking
                ? '查看时长统计'
                : '该服务未启用计时功能'
            }
          >
            时长统计
          </Button>
          <Popconfirm
            title="确认删除？"
            onConfirm={() => {
              handleDel(record);
            }}
          >
            <Button type="link" danger>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <>
      <ProTable<API.AdditionalServiceAttrs>
        actionRef={actionRef}
        rowKey="id"
        columns={columns}
        request={async (params) => {
          const { errCode, msg, data } = await index(params);
          if (errCode) {
            message.error(msg || '列表查询失败');
            return {
              data: [],
              total: 0,
            };
          }
          return {
            data: data?.list || [],
            total: data?.total || 0,
          };
        }}
        toolBarRender={() => [
          <Button
            key="add"
            type="primary"
            onClick={() => {
              setCurrent(undefined);
              setModalVisible(true);
            }}
          >
            新增
          </Button>,
        ]}
      />
      <EditModal
        open={modalVisible}
        info={current}
        onClose={() => setModalVisible(false)}
        onSave={handleSave}
      />
      <AdditionalServiceDurationModal
        open={durationModalVisible}
        additionalService={current}
        onClose={() => {
          setDurationModalVisible(false);
          setCurrent(undefined);
        }}
      />
    </>
  );
};

export default connect(({ dictionarie }) => ({ dictionarie }))(
  AdditionalService,
);
