# 增项服务计时功能支持

## 概述
为增项服务管理界面添加了 `needDurationTracking` 字段的支持，允许管理员控制哪些增项服务需要参与时长统计和计时功能。

## 修改内容

### 1. 类型定义更新
**文件**: `src/services/typings.d.ts`
- 新增 `AdditionalServiceAttrs` 接口定义
- 添加 `needDurationTracking?: boolean` 字段
- 用于管理界面的增项服务属性控制

### 2. 增项服务列表页面
**文件**: `src/pages/serviceManager/Service/AdditionalService/index.tsx`
- 新增"是否需要计时"列，显示计时状态
- 更新"时长统计"按钮逻辑：
  - 当 `needDurationTracking` 为 `false` 时禁用按钮
  - 添加提示信息说明禁用原因

### 3. 增项服务编辑模态框
**文件**: `src/pages/serviceManager/Service/AdditionalService/EditModal.tsx`
- 导入 `ProFormSwitch` 组件
- 添加"是否需要计时"开关控件
- 包含提示信息解释该功能的作用

### 4. 服务关联增项服务管理
**文件**: `src/pages/serviceManager/Service/EditAdditionalModal.tsx`
- 在增项服务选择表格中添加"价格"和"是否计时"列
- 帮助管理员了解每个增项服务的计时状态

## 功能特性

### 计时状态显示
- ✅ **启用计时**: 绿色"是"标识
- ❌ **禁用计时**: 灰色"否"标识

### 时长统计按钮控制
- **启用状态**: 可点击查看时长统计
- **禁用状态**: 按钮禁用，显示"该服务未启用计时功能"提示

### 编辑界面
- 开关控件支持启用/禁用计时功能
- 包含详细的功能说明提示

## 使用说明

1. **新增增项服务**: 在编辑模态框中可以设置是否需要计时
2. **编辑现有服务**: 可以修改计时设置
3. **查看时长统计**: 只有启用计时的服务才能查看统计数据
4. **服务关联管理**: 在关联增项服务时可以看到每个服务的计时状态

## 技术实现

### 数据流
1. 前端表单提交 `needDurationTracking` 字段
2. 后端存储该字段值
3. 列表页面根据该字段控制功能可用性
4. 时长统计功能仅对启用计时的服务生效

### 兼容性
- 新增字段为可选字段，不影响现有数据
- 未设置该字段的服务默认为不启用计时
- 向后兼容现有的时长统计功能

## 注意事项

1. 该功能需要后端API支持 `needDurationTracking` 字段的存储和返回
2. 时长统计功能的具体实现依赖于后端的计时逻辑
3. 建议在生产环境部署前进行充分测试
